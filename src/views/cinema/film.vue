<route lang="yaml">
meta:
  title: 影片管理
  icon: i-ri:film-line
  auth: /data/film/query
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'

defineOptions({
  name: 'CinemaFilm',
})

// 影片数据接口
interface FilmMarker {
  code: string
  names: string[]
  role: string
}

interface Film {
  id: string
  cinemaCode: string
  code: string
  name: string
  lang: string
  duration: number
  sequence: number
  version: string
  publishDate: number
  markers: FilmMarker[]
  introduction: string
  provider: string
}

// 响应式数据
const loading = ref(false)
const filmList = ref<Film[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索条件
const searchForm = ref({
  filmName: '',
  version: '',
  provider: '',
})

// 获取影片列表
async function getFilmList() {
  loading.value = true
  try {
    // TODO: 替换为实际的API调用
    // const res = await apiCinema.searchFilms({
    //   page: currentPage.value - 1,
    //   size: pageSize.value,
    //   filmName: searchForm.value.filmName || undefined,
    //   version: searchForm.value.version || undefined,
    //   provider: searchForm.value.provider || undefined,
    // })

    // 模拟数据
    const mockData: Film[] = [
      {
        id: '1',
        cinemaCode: 'CINEMA001',
        code: 'FILM001',
        name: '阿凡达：水之道',
        lang: '中文',
        duration: 192,
        sequence: 1,
        version: '3D IMAX',
        publishDate: Date.now() - 86400000,
        markers: [
          {
            code: 'DIRECTOR001',
            names: ['詹姆斯·卡梅隆'],
            role: '导演'
          },
          {
            code: 'ACTOR001',
            names: ['萨姆·沃辛顿', '佐伊·索尔达娜'],
            role: '主演'
          }
        ],
        introduction: '《阿凡达：水之道》是一部科幻冒险电影，讲述了杰克·萨利一家在潘多拉星球上的新冒险。',
        provider: '20世纪影业'
      },
      {
        id: '2',
        cinemaCode: 'CINEMA001',
        code: 'FILM002',
        name: '流浪地球2',
        lang: '中文',
        duration: 173,
        sequence: 1,
        version: '2D',
        publishDate: Date.now() - 172800000,
        markers: [
          {
            code: 'DIRECTOR002',
            names: ['郭帆'],
            role: '导演'
          },
          {
            code: 'ACTOR002',
            names: ['吴京', '刘德华', '李雪健'],
            role: '主演'
          }
        ],
        introduction: '《流浪地球2》是一部中国科幻电影，讲述了人类为拯救地球而进行的壮举。',
        provider: '中国电影'
      },
      {
        id: '3',
        cinemaCode: 'CINEMA001',
        code: 'FILM003',
        name: '满江红',
        lang: '中文',
        duration: 159,
        sequence: 1,
        version: '2D',
        publishDate: Date.now() - 259200000,
        markers: [
          {
            code: 'DIRECTOR003',
            names: ['张艺谋'],
            role: '导演'
          },
          {
            code: 'ACTOR003',
            names: ['沈腾', '易烊千玺', '张译'],
            role: '主演'
          }
        ],
        introduction: '《满江红》是一部古装悬疑喜剧电影，以岳飞《满江红》为背景展开的故事。',
        provider: '欢喜传媒'
      }
    ]

    // 模拟搜索过滤
    let filteredData = mockData
    if (searchForm.value.filmName) {
      filteredData = filteredData.filter(item =>
        item.name.includes(searchForm.value.filmName)
      )
    }
    if (searchForm.value.version) {
      filteredData = filteredData.filter(item =>
        item.version.includes(searchForm.value.version)
      )
    }
    if (searchForm.value.provider) {
      filteredData = filteredData.filter(item =>
        item.provider.includes(searchForm.value.provider)
      )
    }

    filmList.value = filteredData
    total.value = filteredData.length
  }
  catch (error: any) {
    toast.error(error.message || '获取影片列表失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  getFilmList()
}

// 重置搜索
function handleReset() {
  searchForm.value = {
    filmName: '',
    version: '',
    provider: '',
  }
  handleSearch()
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  getFilmList()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  getFilmList()
}

// 格式化时间
function formatDate(timestamp: number) {
  return new Date(timestamp).toLocaleDateString()
}

// 格式化时长
function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`
}

// 格式化演职人员
function formatMarkers(markers: FilmMarker[]) {
  return markers.map(marker =>
    `${marker.role}: ${marker.names.join('、')}`
  ).join(' | ')
}

// 详情对话框相关
const detailDialogVisible = ref(false)
const currentFilm = ref<Film | null>(null)

// 查看详情
function handleViewDetail(film: Film) {
  currentFilm.value = film
  detailDialogVisible.value = true
}

// 初始化
onMounted(() => {
  getFilmList()
})
</script>

<template>
  <div>
    <FaPageHeader title="影片管理" description="管理影院的影片信息，包括影片详情、演职人员等" />

    <FaPageMain>
      <!-- 搜索区域 -->
      <div class="mb-4 flex flex-wrap items-center gap-4">
        <el-input
          v-model="searchForm.filmName"
          placeholder="影片名称"
          clearable
          style="width: 200px;"
          @keyup.enter="handleSearch"
        />
        <el-input
          v-model="searchForm.version"
          placeholder="影片类型"
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />
        <el-input
          v-model="searchForm.provider"
          placeholder="提供商"
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />

        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          重置
        </el-button>
      </div>

      <!-- 影片表格 -->
      <el-table
        v-loading="loading"
        :data="filmList"
        border
        stripe
      >
        <el-table-column prop="code" label="影片编码" width="120" />
        <el-table-column prop="name" label="影片名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="version" label="发行版本" width="100" />
        <el-table-column prop="lang" label="语言" width="80" />
        <el-table-column prop="duration" label="时长" width="120">
          <template #default="scope">
            {{ formatDuration(scope.row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="公映日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.publishDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="提供商" width="120" />
        <el-table-column prop="markers" label="演职人员" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            {{ formatMarkers(scope.row.markers) }}
          </template>
        </el-table-column>
        <el-table-column prop="introduction" label="简介" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              text
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:view" />
              </template>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 影片详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="影片详情"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="currentFilm" class="space-y-4">
        <!-- 基本信息 -->
        <FaCard title="基本信息">
          <div class="grid grid-cols-2 gap-4">
            <div><strong>影片编码:</strong> {{ currentFilm.code }}</div>
            <div><strong>影片名称:</strong> {{ currentFilm.name }}</div>
            <div><strong>影院编码:</strong> {{ currentFilm.cinemaCode }}</div>
            <div><strong>语言:</strong> {{ currentFilm.lang }}</div>
            <div><strong>时长:</strong> {{ formatDuration(currentFilm.duration) }}</div>
            <div><strong>发行版本:</strong> {{ currentFilm.version }}</div>
            <div><strong>序列号:</strong> {{ currentFilm.sequence }}</div>
            <div><strong>公映日期:</strong> {{ formatDate(currentFilm.publishDate) }}</div>
            <div><strong>提供商:</strong> {{ currentFilm.provider }}</div>
          </div>
        </FaCard>

        <!-- 演职人员信息 -->
        <FaCard title="演职人员">
          <div v-if="currentFilm.markers.length > 0" class="space-y-3">
            <div v-for="marker in currentFilm.markers" :key="marker.code" class="border rounded p-3">
              <div class="flex items-center gap-4">
                <div class="font-medium text-blue-600">{{ marker.role }}</div>
                <div class="flex-1">
                  <el-tag
                    v-for="name in marker.names"
                    :key="name"
                    class="mr-2"
                    type="info"
                  >
                    {{ name }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-400">暂无演职人员信息</div>
        </FaCard>

        <!-- 影片简介 -->
        <FaCard title="影片简介">
          <div class="text-gray-700 leading-relaxed">
            {{ currentFilm.introduction || '暂无简介' }}
          </div>
        </FaCard>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
