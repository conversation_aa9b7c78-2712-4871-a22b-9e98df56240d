<script setup lang="ts">
import { Connection, Refresh, Search, View } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import apiCinema from '@/api/modules/cinema'

// 响应式数据
const tableData = ref([])
const total = ref<number>(0)
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,
  size: 10,
  pageSizes: [10, 20, 50],
})
const searchForm = reactive({
  cinemaName: '',
  cinemaCode: '',
  city: '',
  provider: '',
})

const editDialogVisible = ref(false)
const currentCinema = ref({
  name: '',
  code: '',
  province: '',
  city: '',
  provider: '',
  address: '',
  phone: '',
  email: '',
  website: '',
  description: '',
  status: 1,
  createTime: '',
  updateTime: '',
  contactPhone: '',
})

// 获取影院列表
async function getCinemaList() {
  loading.value = true
  try {
    const res = await apiCinema.getCinemaList({
      page: pagination.currentPage,
      size: pagination.size,
      ...searchForm,
    })
    // const { data, code } = res
    const { total: totalCount, content } = res.data
    tableData.value = content
    total.value = totalCount
  }
  catch (e) {
    console.log(e)
    ElMessage.error('获取影院列表失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.currentPage = 1
  getCinemaList()
}

// 重置
function handleReset() {
  Object.assign(searchForm, {
    cinemaName: '',
    cinemaCode: '',
    city: '',
    provider: '',
  })
  pagination.currentPage = 1
  getCinemaList()
}

// 分页大小变化
function handleSizeChange(val: number) {
  pagination.size = val
  getCinemaList()
}

// 当前页变化
function handleCurrentChange(val: number) {
  pagination.currentPage = val
  getCinemaList()
}

// 查看详情
async function handleViewDetail(row: any) {
  loading.value = true
  try {
    const res = await apiCinema.getCinemaDetail({ cinemaCode: row.code })
    console.log(res)

    if (res.data.code === 0) {
      currentCinema.value = res.data.data
      editDialogVisible.value = true
    }
  }
  catch {
    ElMessage.error('获取影院详情失败')
  }
  finally {
    loading.value = false
  }
}

// 生成二维码
async function handleGenerateQrcode(row: any) {
  loading.value = true
  try {
    const res = await apiCinema.createCinemaQrcode({ cinemaCode: row.code })
    if (res.data.code === 0) {
      ElMessage.success('二维码生成成功')
      getCinemaList()
    }
  }
  catch {
    ElMessage.error('二维码生成失败')
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  getCinemaList()
})
</script>

<template>
  <div>
    <fa-page-header title="影院管理" description="影院管理页面" />
    <fa-page-main>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="影院名称">
          <el-input v-model="searchForm.cinemaName" placeholder="请输入影院名称" clearable />
        </el-form-item>
        <el-form-item label="影院编码">
          <el-input v-model="searchForm.cinemaCode" placeholder="请输入影院编码" clearable />
        </el-form-item>
        <el-form-item label="城市">
          <el-input v-model="searchForm.city" placeholder="请输入城市" clearable />
        </el-form-item>
        <el-form-item label="供应商">
          <el-input v-model="searchForm.provider" placeholder="请输入供应商" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 影院列表 -->
      <el-table v-loading="loading" :data="tableData" border style="width: 100%;">
        <el-table-column prop="name" label="影院名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="code" label="影院编码" width="120" />
        <el-table-column label="地区" width="180">
          <template #default="{ row }">
            {{ row.province }} {{ row.city }}
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="供应商" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="screenCount" label="影厅数" width="90" align="center" />
        <el-table-column label="定价模式" width="120">
          <template #default="{ row }">
            {{ row.priceType === 1 ? '合作商定价' : '影院定价' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="90">
          <template #default="{ row }">
            <el-tag :type="row.syncStatus === 1 ? 'success' : 'danger'">
              {{ row.syncStatus === 1 ? '正常' : '被隔离' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ row.createDate }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link :icon="View" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button v-if="!row.qrCodeUrl" type="success" link :icon="Connection" @click="handleGenerateQrcode(row)">
              生成二维码
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage" v-model:page-size="pagination.size"
          :page-sizes="pagination.pageSizes" :total="total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange"
        />
      </div>
    </fa-page-main>

    <!-- 修改详情弹窗 -->
    <el-dialog v-model="editDialogVisible" title="影院详情" width="600px">
      <el-form v-if="currentCinema" label-width="100px">
        <el-form-item label="影院名称">
          {{ currentCinema.name }}
        </el-form-item>
        <el-form-item label="影院编码">
          {{ currentCinema.code }}
        </el-form-item>
        <el-form-item label="所在省份">
          {{ currentCinema.province }}
        </el-form-item>
        <el-form-item label="所在城市">
          {{ currentCinema.city }}
        </el-form-item>
        <el-form-item label="供应商">
          {{ currentCinema.provider }}
        </el-form-item>
        <el-form-item label="地址">
          {{ currentCinema.address }}
        </el-form-item>
        <el-form-item label="影厅数">
          {{ currentCinema.screenCount }}
        </el-form-item>
        <el-form-item label="定价模式">
          {{ currentCinema.priceType === 1 ? '合作商定价' : '影院定价' }}
        </el-form-item>
        <el-form-item label="系统版本">
          {{ currentCinema.version === 2 ? 'CA证书23规范' : 'UsbKey13规范' }}
        </el-form-item>
        <el-form-item label="状态">
          <el-tag :type="currentCinema.syncStatus === 1 ? 'success' : 'danger'">
            {{ currentCinema.syncStatus === 1 ? '正常' : '被隔离' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="授权日期">
          {{ currentCinema.createDate }}
        </el-form-item>
        <el-form-item label="创建时间">
          {{ new Date(currentCinema.ctime).toLocaleString() }}
        </el-form-item>
        <el-form-item label="更新时间">
          {{ new Date(currentCinema.uptime).toLocaleString() }}
        </el-form-item>
        <el-form-item v-if="currentCinema.qrCodeUrl" label="二维码">
          <el-image
            :src="currentCinema.qrCodeUrl" :preview-src-list="[currentCinema.qrCodeUrl]" fit="contain"
            style="width: 200px; height: 200px;"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.cinema-management-page {
  padding: 20px;
}

.search-card {
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
