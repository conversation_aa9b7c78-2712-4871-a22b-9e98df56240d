<route lang="yaml">
meta:
  title: 字典选择器演示
  icon: i-ri:code-line
</route>

<script setup lang="ts">
import { ref } from 'vue'
import DictSelector from '@/components/DictSelector/index.vue'

defineOptions({
  name: 'DictionaryDemo',
})

// 演示数据
const formData = ref({
  userStatus: '',
  userType: '',
  priority: '',
  orderStatus: [],
  paymentMethod: '',
})

// 处理选择变化
function handleUserStatusChange(value: any, option: any) {
  console.log('用户状态变化:', value, option)
}

function handleUserTypeChange(value: any, option: any) {
  console.log('用户类型变化:', value, option)
}

function handleOrderStatusChange(value: any, option: any) {
  console.log('订单状态变化:', value, option)
}
</script>

<template>
  <div>
    <FaPageHeader title="字典选择器演示" description="展示字典选择器组件的各种使用方式" />
    
    <FaPageMain>
      <div class="space-y-6">
        <!-- 基础用法 -->
        <FaCard title="基础用法" description="最简单的字典选择器使用方式">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">用户状态</label>
              <DictSelector
                v-model="formData.userStatus"
                dict-type="user_status"
                placeholder="请选择用户状态"
                @change="handleUserStatusChange"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ formData.userStatus }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-2">用户类型</label>
              <DictSelector
                v-model="formData.userType"
                dict-type="user_type"
                placeholder="请选择用户类型"
                @change="handleUserTypeChange"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ formData.userType }}</p>
            </div>
          </div>
        </FaCard>

        <!-- 多选模式 -->
        <FaCard title="多选模式" description="支持选择多个选项">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">订单状态（多选）</label>
              <DictSelector
                v-model="formData.orderStatus"
                dict-type="order_status"
                multiple
                placeholder="请选择订单状态"
                @change="handleOrderStatusChange"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ JSON.stringify(formData.orderStatus) }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-2">优先级（可搜索）</label>
              <DictSelector
                v-model="formData.priority"
                dict-type="priority_level"
                filterable
                placeholder="请输入或选择优先级"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ formData.priority }}</p>
            </div>
          </div>
        </FaCard>

        <!-- 自定义配置 -->
        <FaCard title="自定义配置" description="展示不同的配置选项">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">支付方式（小尺寸）</label>
              <DictSelector
                v-model="formData.paymentMethod"
                dict-type="payment_method"
                size="small"
                placeholder="请选择支付方式"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ formData.paymentMethod }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-2">用户状态（显示所有状态）</label>
              <DictSelector
                v-model="formData.userStatus"
                dict-type="user_type"
                show-all
                placeholder="包含禁用项"
              />
              <p class="text-xs text-gray-500 mt-1">选中值: {{ formData.userStatus }}</p>
            </div>
          </div>
        </FaCard>

        <!-- 表单示例 -->
        <FaCard title="表单中使用" description="在表单中使用字典选择器">
          <el-form :model="formData" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户状态" prop="userStatus">
                  <DictSelector
                    v-model="formData.userStatus"
                    dict-type="user_status"
                    placeholder="请选择用户状态"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户类型" prop="userType">
                  <DictSelector
                    v-model="formData.userType"
                    dict-type="user_type"
                    placeholder="请选择用户类型"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="优先级" prop="priority">
                  <DictSelector
                    v-model="formData.priority"
                    dict-type="priority_level"
                    placeholder="请选择优先级"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="支付方式" prop="paymentMethod">
                  <DictSelector
                    v-model="formData.paymentMethod"
                    dict-type="payment_method"
                    placeholder="请选择支付方式"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </FaCard>

        <!-- 当前表单数据 -->
        <FaCard title="当前表单数据" description="实时显示表单数据">
          <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{{ JSON.stringify(formData, null, 2) }}</pre>
        </FaCard>
      </div>
    </FaPageMain>
  </div>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}
</style>
