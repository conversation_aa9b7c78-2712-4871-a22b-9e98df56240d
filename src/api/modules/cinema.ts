import api from '@/api'

// 影院管理相关接口

export default {

  // 获取影院列表
  getCinemaList: (params: {
    page: number
    size: number
    cinemaName?: string
    cinemaCode?: string
    city?: string
    provider?: string
  }) =>
    api.post('/cinema/search', params),

  // 获取所有影院列表（不分页）
  getAllCinemaList: () =>
    api.post('/cinema/all'),

  // 获取影院详情
  getCinemaDetail: (params: { cinemaCode: string }) =>
    api.post('/data/cinema/getByCode', params),

  // 生成影院二维码
  createCinemaQrcode: (params: { cinemaCode: string }) =>
    api.post('/cinema/qrcode/create', params),
}
