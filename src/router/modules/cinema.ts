import type { RouteRecordRaw } from 'vue-router'

const cinema: RouteRecordRaw = {
  path: '/cinema',
  component: () => import('@/layouts/index.vue'),
  redirect: '/cinema/index',
  name: 'Cinema',
  meta: {
    title: '影院管理',
    icon: 'ep:video-camera',
    // auth: '/adm_cinema',
  },
  children: [
    {
      path: 'index',
      name: 'CinemaIndex',
      component: () => import('@/views/cinema/cinema.vue'),
      meta: {
        title: '影院列表',
        icon: 'ep:film',
        auth: '/adm_cinema/query',
        // activeMenu: '/cinema',
      },
    },
  ],
}

export default cinema
