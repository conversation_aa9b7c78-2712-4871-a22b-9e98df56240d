<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import apiSystem from '@/api/modules/system'

defineOptions({
  name: 'DictSelector',
})

// 字典项接口
interface DictItem {
  id: string;
  dictType: string;
  dictLabel: string;
  dictValue: string;
  description?: string;
  status: number;
  sort?: number
}

// Props
const props = defineProps<{
  dictType: string // 字典类型;
  modelValue?: string | number | string[] | number[] // 选中的值;
  multiple?: boolean // 是否多选;
  placeholder?: string // 占位符;
  clearable?: boolean // 是否可清空;
  disabled?: boolean // 是否禁用;
  size?: 'large' | 'default' | 'small' // 尺寸;
  filterable?: boolean // 是否可搜索;
  valueKey?: 'dictValue' | 'dictLabel' // 使用哪个字段作为值，默认使用dictValue;
  labelKey?: 'dictLabel' | 'dictValue' // 使用哪个字段作为显示文本，默认使用dictLabel;
  showAll?: boolean // 是否显示所有状态的字典项（包括禁用的）
}>()

const propsWithDefaults = withDefaults(props, {
  modelValue: undefined,
  multiple: false,
  placeholder: '请选择',
  clearable: true,
  disabled: false,
  size: 'default',
  filterable: false,
  valueKey: 'dictValue',
  labelKey: 'dictLabel',
  showAll: false,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | string[] | number[] |; undefined]
  'change': [;value: string | number | string[] | number[] | undefined,; option: DictItem | DictItem[] | undefined]
}>()

// 响应式数据
const loading = ref(false)
const dictItems = ref<DictItem[]>([])

// 计算属性 - 过滤后的字典项
// 计算属性 - 过滤后的字典项
const filteredDictItems = computed(() => {
  if (propsWithDefaults.showAll) {
    return dictItems.value
  }
  return dictItems.value.filter(item => item.status === 1)
})

// 计算属性 - 选项列表
const options = computed(() => {
  return filteredDictItems.value
    .sort((a, b) => (a.sort || 0) - (b.sort || 0))
    .map(item => ({
      label: item[propsWithDefaults.labelKey],
      value: item[propsWithDefaults.valueKey],
      disabled: item.status === 0,
      raw: item,
    }))
})
// 获取字典项列表
async function getDictItems() {
  async function getDictItems() {
    if (!propsWithDefaults.dictType) {
      return
    }

    loading.value = true
    try {
      const res: any = await apiSystem.getDictItemsByType({
        dictType: propsWithDefaults.dictType,
        status: propsWithDefaults.showAll ? undefined : 1,
      })
      if (res.code === 0) {
        dictItems.value = res.data || []
      }
      else {
        console.error('获取字典项失败:', res.msg)
        dictItems.value = []
      }
    }
    catch (error: any) {
      console.error('获取字典项失败:', error.message)
      dictItems.value = []
    }
    finally {
      loading.value = false
    }
  }
  // 处理值变化
  function handleChange(value: string | number | string[] | number[] | undefined) {
    function handleChange(value: string | number | string[] | number[] | undefined) {
      emit('update:modelValue', value)

      // 查找对应的选项
      let option: DictItem | DictItem[] | undefined
      if (propsWithDefaults.multiple && Array.isArray(value)) {
        option = dictItems.value.filter(item => value.includes(item[propsWithDefaults.valueKey] as any))
      }
      else {
        option = dictItems.value.find(item => item[propsWithDefaults.valueKey] === value)
      }

      emit('change', value, option)
    }
    // 监听字典类型变化
    watch(
      watch(
        () => propsWithDefaults.dictType,
        () => {
          getDictItems()
        },
        { immediate: true },
      )

// 监听showAll变化
watch(
        () => propsWithDefaults.showAll,
        () => {
          getDictItems()
        },
      )
// 暴露方法
defineExpose({
        refresh: getDictItems,
        getDictItems,
      })
</script>

<template>
  <el-select <el-select :model-value="propsWithDefaults.modelValue" :multiple="propsWithDefaults.multiple"
    :placeholder="propsWithDefaults.placeholder" :clearable="propsWithDefaults.clearable"
    :disabled="propsWithDefaults.disabled" :size="propsWithDefaults.size" :filterable="propsWithDefaults.filterable"
    :loading="loading" @update:model-value="handleChange">
    <el-option v-for="option in options" :key="option.value" :label="option.label" :value="option.value"
      :disabled="option.disabled">
      <span>{{ option.label }}</span>
      <span v-if="option.raw.description" class="text-gray-400 text-xs ml-2">
        {{ option.raw.description }}
      </span>
    </el-option>
  </el-select>
}}
